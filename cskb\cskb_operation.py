import re
import fitz
import json
import datetime
import requests
from tqdm import tqdm

import os
import sys
# 添加上上层目录到系统路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from database.mysql import MySQLDatabase
from config import KNOWLEDGE_FIELD_CSKB_ENV, CSKB_ADDRESS_DICT, KNOWLEDGE_FIELDS
from env import ENVIRONMENT
from utils.data_mapper import map_doc_result, map_faq_result, get_doc_sql_fields, get_faq_sql_fields, FAQ_TARGET_FIELDS, DOC_TARGET_FIELDS
from utils.utils import html_to_text
from utils.request import HttpClient




class CSKBOperation:
    def __init__(self, db: str = 'cskb'):
        self.db = MySQLDatabase(db)
        self.env = ENVIRONMENT
        # self.cskb_agents_info = self.get_cskb_agents_info()
        self.all_dir_tree_dict = {}

    def _build_headers(self, access_token: str) -> dict:
        """构建CSKB专用的请求头"""
        return HttpClient.build_headers(authorization=access_token)

    def _build_cskb_url(self, endpoint: str) -> str:
        """构建CSKB API URL"""
        cskb_url = CSKB_ADDRESS_DICT[self.env]['cskb_url']
        return f"{cskb_url}{endpoint}"

    def _make_request(self, url: str, headers: dict, params: dict = None, method: str = 'GET') -> dict:
        """统一的HTTP请求方法"""
        return HttpClient.make_request(url, headers, params=params, method=method)

    def _paginated_request(self, agent: dict, endpoint: str, page_size: int = 100, max_pages: int = 9999) -> list:
        """通用的分页请求方法"""
        headers = self._build_headers(agent['access_token'])
        base_url = self._build_cskb_url(endpoint)
        return HttpClient.paginated_request(
            base_url=base_url,
            headers=headers,
            page_size=page_size,
            max_pages=max_pages
        )

    # def get_cskb_agents_info(self) -> list:
    #     """获取CSKB知识库的agent_id和access_token"""
    #     sql = f'SELECT * FROM cskb_agent_key where env = \'{self.env}\''
    #     results = self.db.execute_query(sql)
    #     return results

    # 查询知识库文档具体内容
    def get_agent_doc_detail(self, agent, doc_id):
        headers = self._build_headers(agent['access_token'])
        url = self._build_cskb_url(f"/cskb/open/api/v1/doc/detail?id={doc_id}")

        answer_json = self._make_request(url, headers)

        if "data" not in answer_json:
            return None

        data = answer_json['data']
        html_answer = data['richText']
        text_answer = html_to_text(html_answer)

        agent_name = agent['agent_name']

        doc_info = {
            "id": data['id'],
            "id_parent": "",
            "title": data['title'],
            "agentId": data['agentId'],
            "agentName": agent_name,
            "dirId": data['dirId'],
            "originalId": data['originalId'],
            "originalId_parent": "",
            "pubUserName": data['pubUserName'],
            "pubTime": data['pubTime'],
            "version": data['version'],
            "tags": data['tags'],
            "attaches": data['attaches'],
            "wordNum": data['wordNum'],
            "pictureNum": data['pictureNum'],
            "linkNum": data['linkNum'],
            "docText": text_answer,
            "docHtml": html_answer,
        }

        return doc_info

    # 读取附件
    def get_agent_doc_attach_detail(self, agent, doc_title, attach_title, attach_id):
        headers = self._build_headers(agent['access_token'])
        cskb_attach_download_url = CSKB_ADDRESS_DICT[self.env]['cskb_attach_download_url']

        attach_content = f"BM/询价单名称：{doc_title}，附件名称：{attach_title}，附件内容如下：\n\n"

        attach_download_url = f"{cskb_attach_download_url}/cskb/storage/v1/download"
        param = {
            "id":attach_id,
            "preview": "true"
        }

        try:
            response = requests.get(attach_download_url, headers=headers, params=param)
        except Exception as error:
            print(f"附件{attach_id}下载失败: {error}")
            return ""
        
        if response.status_code == 200:
            # 获取内容类型
            content_type = response.headers.get('Content-Type', '')

            # 处理 PDF 响应
            if 'application/pdf' in content_type:
                try:
                    pdf = fitz.open(stream=response.content, filetype="pdf")  # type: ignore

                    for page in pdf:
                        text = page.get_text(sort=True)
                        text = re.sub(r"\s*\n\s*", "\n", text)
                        # @TODO:超过10000字符的部分不读取
                        if len(attach_content) > 1000000:
                            break
                        attach_content += text.strip()
                except Exception as error:
                    print(f"附件{attach_id}读取失败: {error}")

                finally:
                    pass
                
            # 处理图片响应
            elif 'image/' in content_type:
                # 暂时不对图片文件进行处理
                pass

            else:
                print(f"附件id:{attach_id}, 响应内容不是 PDF 或图片")
        else:
            print(f"附件id:{attach_id}, 请求失败，状态码: {response.status_code}")

        return attach_content

    def _ensure_attaches_is_list(self, doc):
        """确保文档的attaches字段是列表格式"""
        if 'attaches' in doc:
            attaches = doc['attaches']
            if isinstance(attaches, str) and attaches.strip():
                try:
                    doc['attaches'] = json.loads(attaches)
                except (json.JSONDecodeError, ValueError) as e:
                    print(f"附件字段JSON解析失败: {e}, 原值: {attaches}")
                    doc['attaches'] = []
            elif not isinstance(attaches, list):
                doc['attaches'] = []

    def _ensure_tags_is_list(self, doc):
        """确保文档的tags字段是列表格式"""
        if 'tags' in doc:
            tags = doc['tags']
            if isinstance(tags, str) and tags.strip():
                try:
                    doc['tags'] = json.loads(tags)
                except (json.JSONDecodeError, ValueError) as e:
                    print(f"标签字段JSON解析失败: {e}, 原值: {tags}")
                    doc['tags'] = []
            elif not isinstance(tags, list):
                doc['tags'] = []

    def _enrich_document_with_directory_info(self, doc, dir_tree_dict, agent_name):
        """为文档添加目录信息"""
        doc['agentName'] = agent_name
        doc_dir_id = doc['dirId']
        doc['dir_name'] = f"[{agent_name}] {dir_tree_dict[doc_dir_id]['full_name']}"
        doc['dir_level'] = dir_tree_dict[doc_dir_id]['level']

    def _process_attachment(self, attach, doc, agent):
        """处理单个附件"""
        # 附件文档中部分key，用其他key值替换
        attach['originalId'] = attach['id']
        attach['title'] = attach['name']

        # 附件的基本信息和其关联主文档保持一致
        attach['agentId'] = doc['agentId']
        attach['agentName'] = doc['agentName']
        attach['dirId'] = doc['dirId']
        attach['dir_name'] = doc['dir_name']
        attach['dir_level'] = doc['dir_level']

        # 获取附件内容
        attach_text = self.get_agent_doc_attach_detail(
            agent=agent,
            doc_title=doc['title'],
            attach_title=attach['title'],
            attach_id=attach['previewId']
        )
        attach['docText'] = attach_text
        attach["id_parent"] = doc['id']
        attach['originalId_parent'] = doc['originalId']

        return attach

    def _process_document_with_attachments(self, doc, agent, dir_tree_dict):
        """处理单个文档及其附件的通用方法"""
        knowledge_list = []

        # 确保JSON字段格式正确
        self._ensure_attaches_is_list(doc)
        self._ensure_tags_is_list(doc)

        # 验证文档内容
        doc_text = doc.get('docText', '')
        doc_html = doc.get('docHtml', '')
        doc_attaches = doc.get('attaches', [])

        if not doc_text and not doc_html and not doc_attaches:
            return knowledge_list

        # 添加目录信息
        self._enrich_document_with_directory_info(doc, dir_tree_dict, agent['agent_name'])

        # 添加文档到知识列表
        knowledge_list.append({"type": "doc", "data": doc})

        # 处理附件
        if doc_attaches:
            print('doc_attaches', doc_attaches)
            for attach in doc_attaches:
                print('attach', attach)
                processed_attach = self._process_attachment(attach, doc, agent)
                knowledge_list.append({"type": "attach", "data": processed_attach})

        return knowledge_list

    def get_agent_docs_and_attaches(self, agent):
        """根据文档id查询文档内容"""

        print('开始获取文档和附件')

        docs = self.get_agent_all_doc_ids(agent)

        knowledge_list = []
        # 获取Agent目录清单
        dir_tree_dict = self.get_directory_tree(agent)

        # 初始化带进度条的迭代器
        progress = tqdm(
            docs,
            desc=f"处理 {agent['agent_name']}",
            bar_format="{l_bar}{bar:20}{r_bar}",
            ncols=100,  # 控制进度条总宽度
            mininterval=0.5  # 更新频率
        )

        for doc in progress:
            doc_id = doc['id']
            doc_detail = self.get_agent_doc_detail(agent=agent, doc_id=doc_id)
            if not doc_detail:
                continue

            # 将doc_detail的内容更新到doc中
            doc.update(doc_detail)

            # 使用通用方法处理文档和附件
            doc_knowledge_list = self._process_document_with_attachments(doc, agent, dir_tree_dict)
            knowledge_list.extend(doc_knowledge_list)
            
            break

        return knowledge_list


    # 获取所有文档
    def get_agent_all_doc_ids(self, agent):
        results = self._paginated_request(agent, "/cskb/open/api/v1/doc")

        docs_list = []
        for ans in results:
            doc_info = {
                "id": ans['id'],
                "originalId": ans['originalId'],
            }
            docs_list.append(doc_info)

        return docs_list

    def get_agent_all_knowledge(self, agent):
        docs_list = self.get_agent_docs_and_attaches(agent)
        faqs_list = self.get_agent_all_faqs(agent)
        knowledge_list = docs_list + faqs_list
        return knowledge_list

    # 获取所有FAQ
    def get_agent_all_faqs(self, agent):

        print('开始获取faq')

        faq_list = []

        # 获取Agent目录清单
        dir_tree_dict = self.get_directory_tree(agent)

        results = self._paginated_request(agent, "/cskb/open/api/v1/faq/standard", max_pages=999)

        for ans in results:
            agent_id = ans['agentId']
            agent_name = agent['agent_name']

            question_text = ans['question']
            answer_json = json.loads(ans['answer'])['list'][0]
            if answer_json['type'] == 3:
                answer_text_html = answer_json['text']
                answer_text = re.sub('<.*?>', '', answer_text_html).strip()
            else:
                answer_text = answer_json['text']

            faq_info = {
                "id": ans['id'],
                "agentId": agent_id,
                "agentName": agent_name,
                "question": question_text,
                "answer": answer_text,
                "dirId": ans['dirId'],
                "originalId": ans['originalId'],
                "pubUserName": ans['pubUserName'],
                "pubTime": ans['pubTime'],
                "versionStatus": ans['versionStatus'],
            }

            faq_dir_id = faq_info['dirId']
            faq_info['dir_name'] = f"[{faq_info['agentName']}] {dir_tree_dict[faq_dir_id]['full_name']}"
            faq_info['dir_level'] = dir_tree_dict[faq_dir_id]['level']

            faq_list.append({"type": "faq", "data": faq_info})

            break  # 保留原有的break逻辑

        return faq_list


    # 获取目录结构
    def get_directory_tree(self, agent):
        headers = self._build_headers(agent['access_token'])
        url = self._build_cskb_url("/cskb/open/api/v1/directory?type=1")

        answer_dict = {}

        response_data = self._make_request(url, headers)
        if 'data' not in response_data:
            return answer_dict

        answer = response_data['data']

        # 将answer中完整目录树List转化成Dictionary，Key为dir_id，Value为完整目录名
        ## 第一步：创建一个临时Dictionary，Key为dir_id，Value为{底层目录名称，父目录ID，完整目录名称（由第二步逐层补充完整）}
        temp_dir_dict = {}
        for dir in answer:
            dir_id = dir['id']
            dir_name = dir['name']
            dir_parent_id = dir['parentId']
            temp_dir_dict[dir_id] = {"name": dir_name, "parent_id": dir_parent_id, "full_dir_name": dir_name}
        ## 第二步：逐层补充完整目录名称，并保存到一个global Dictionary中，Key为dir_id，Value为完整目录名称
        for dir in answer:
            dir_id = dir['id']
            dir_name = dir['name']
            dir_level = dir['level']
            dir_parent_id = dir['parentId']
            dir_full_name = dir_name
            while (dir_level > 1):
                dir_parent_name = temp_dir_dict[dir_parent_id]['full_dir_name']
                dir_full_name = f"{dir_parent_name}/" + dir_full_name
                dir_parent_id = temp_dir_dict[dir_parent_id]['parent_id']
                dir_level -= 1
            
            answer_dict[dir_id] = {"name": dir_name, "full_name": dir_full_name, "level": dir['level']}
        
        return answer_dict
            

    def get_cskb_incremental_knowledge(self, agent_id: str, knowledge_type: str, query_date: str = None) -> list:
        """"根据agent_id和时间获取新增和更新的CSKB知识"""
        # 确定表名和字段列表
        if knowledge_type == 'doc':
            table = 'cskb_doc_pub'
            fields = get_doc_sql_fields()  # 获取需要的字段列表
        elif knowledge_type == 'faq':
            table = 'cskb_faq_pub'
            fields = get_faq_sql_fields()  # 获取需要的字段列表
        else:
            raise ValueError(f"不支持的知识类型: {knowledge_type}")

        # 处理查询日期
        if not query_date:
            yesterday = datetime.date.today() - datetime.timedelta(days=1)
            query_date = yesterday.strftime('%Y-%m-%d')
        else:
            query_date = datetime.datetime.strptime(query_date, '%Y-%m-%d %H:%M:%S').strftime('%Y-%m-%d')

        # 使用参数化查询，只查询需要的字段
        sql = f'SELECT {fields} FROM {table} WHERE agent_id = %s AND version_status = 1 AND DATE(sys_updated) = %s LIMIT 1'
        results = self.db.execute_query(sql, (agent_id, query_date))

        # 映射查询结果
        mapped_results = []
        if results:
            if knowledge_type == 'doc':
                mapped_result = map_doc_result(results)
            elif knowledge_type == 'faq':
                mapped_result = map_faq_result(results)
            else:
                mapped_result = []

            # 确保返回列表格式
            if isinstance(mapped_result, list):
                mapped_results = mapped_result
            elif mapped_result:
                mapped_results = [mapped_result]

        return mapped_results
        

    def get_cskb_deleted_knowledge(self, agent_id: str, knowledge_type: str, query_date: str = None) -> list:
        """"根据agent_id和时间获取删除的CSKB知识"""
        # 确定表名和字段列表
        if knowledge_type == 'doc':
            table = 'cskb_doc_pub'
            fields = get_doc_sql_fields()  # 获取需要的字段列表
        elif knowledge_type == 'faq':
            table = 'cskb_faq_pub'
            fields = get_faq_sql_fields()  # 获取需要的字段列表
        else:
            raise ValueError(f"不支持的知识类型: {knowledge_type}")

        # 处理查询日期
        if not query_date:
            yesterday = datetime.date.today() - datetime.timedelta(days=1)
            query_date = yesterday.strftime('%Y-%m-%d')
        else:
            query_date = datetime.datetime.strptime(query_date, '%Y-%m-%d %H:%M:%S').strftime('%Y-%m-%d')

        # 使用参数化查询，只查询需要的字段
        sql = f'SELECT {fields} FROM {table} WHERE agent_id = %s AND delete_flag = 1 AND DATE(sys_updated) = %s LIMIT 1'
        results = self.db.execute_query(sql, (agent_id, query_date))

        # 映射查询结果
        mapped_results = []
        if results:
            if knowledge_type == 'doc':
                mapped_result = map_doc_result(results)
            elif knowledge_type == 'faq':
                mapped_result = map_faq_result(results)
            else:
                mapped_result = []

            # 确保返回列表格式
            if isinstance(mapped_result, list):
                mapped_results = mapped_result
            elif mapped_result:
                mapped_results = [mapped_result]

        return mapped_results



    def get_updated_cskb_results(self, agent: dict, query_date: str = None):
        """合并文档和问答的增量结果"""
        
        incremental_docs = cskb_operation.get_cskb_incremental_knowledge(agent_id=agent_id, knowledge_type='doc', query_date=query_date)
        incremental_faqs = cskb_operation.get_cskb_incremental_knowledge(agent_id=agent_id, knowledge_type='faq', query_date=query_date)
        
        
        
        
        knowledge_list = []
        dir_tree_dict = self.get_directory_tree(agent)

        # 初始化带进度条的迭代器
        progress = tqdm(
            doc_list,
            desc=f"处理 {agent['agent_name']} 增量文档",
            bar_format="{l_bar}{bar:20}{r_bar}",
            ncols=100,  # 控制进度条总宽度
            mininterval=0.5  # 更新频率
        )

        for doc in progress:
            # 使用通用方法处理文档和附件
            doc_knowledge_list = self._process_document_with_attachments(doc, agent, dir_tree_dict)
            knowledge_list.extend(doc_knowledge_list)

        # 初始化带进度条的迭代器
        progress = tqdm(
            faq_list,
            desc=f"处理 {agent['agent_name']} 增量FAQ",
            bar_format="{l_bar}{bar:20}{r_bar}",
            ncols=100,  # 控制进度条总宽度
            mininterval=0.5  # 更新频率
        )

        # 处理FAQ列表
        for faq in progress:
            faq_dir_id = faq['dirId']
            faq['dir_name'] = f"[{agent['agent_name']}] {dir_tree_dict[faq_dir_id]['full_name']}"
            faq['dir_level'] = dir_tree_dict[faq_dir_id]['level']

            knowledge_list.append({"type": "faq", "data": faq})
            
        return knowledge_list



if __name__ == "__main__":
    db = 'cskb'
    cskb_operation = CSKBOperation(db=db)
    agent_id = '007c529739690a861ad158e4237fea06'
    agent = {'id': 5, 'agent_id': '007c529739690a861ad158e4237fea06', 'agent_name': '上汽大众客服知识库', 'access_token': '224ee926-559d-4435-b95a-098076e67353', 'env': 'prod'}
    query_date = '2023-07-05 19:54:24'
    knowledge_type = 'doc'
    doc_list = cskb_operation.get_cskb_incremental_knowledge(agent_id=agent_id, query_date=query_date, knowledge_type=knowledge_type)
    knowledge_type = 'faq'
    faq_list = cskb_operation.get_cskb_incremental_knowledge(agent_id=agent_id, query_date=query_date, knowledge_type=knowledge_type)
    # # print(results)
    results = cskb_operation.get_merged_cskb_incremental_results(agent=agent, doc_list=doc_list, faq_list=faq_list)


    # results = cskb_operation.get_cskb_agents_info()
    # agent = {'id': 1, 'agent_id': 'ba96968f61b6b72a6ef97c87ab99bee6', 'agent_name': '零号员工知识库', 'access_token': '99f747e3-687b-4a5f-81c3-51513ea192a5', 'env': 'prod'}
    agent = {'id': 5, 'agent_id': '007c529739690a861ad158e4237fea06', 'agent_name': '上汽大众客服知识库', 'access_token': '224ee926-559d-4435-b95a-098076e67353', 'env': 'prod'}
    # results = cskb_operation.get_agent_all_faqs(agent=agent)
    # doc_id = "c1_4b314f369693207781563a91659f76b8"
    # results = cskb_operation.get_agent_doc_detail(agent=agent,doc_id=doc_id)
    # docs = cskb_operation.get_agent_all_doc_ids(agent) 
    results = cskb_operation.get_agent_all_knowledge(agent)
    print(results)

    